import { MigrationInterface, QueryRunner } from 'typeorm';

export class MakeAppointmentIdNullableInPackageUsageHistory1734000000000 implements MigrationInterface {
  name = 'MakeAppointmentIdNullableInPackageUsageHistory1734000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // SQLite doesn't support ALTER COLUMN directly, so we need to recreate the table
    
    // 1. Create a new table with the correct schema
    await queryRunner.query(`
      CREATE TABLE "package_usage_history_new" (
        "id" integer PRIMARY KEY AUTOINCREMENT NOT NULL,
        "customer_package_id" integer NOT NULL,
        "appointment_id" integer,
        "service_date" datetime NOT NULL,
        "status_at_usage" varchar NOT NULL,
        "notes" text,
        "event_type" varchar(50),
        "created_at" datetime NOT NULL DEFAULT (datetime('now')),
        CONSTRAINT "FK_10b53345d00197396e6db32d22e" FOREIGN KEY ("customer_package_id") REFERENCES "customer_packages" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION,
        CONSTRAINT "FK_fa3bc8922073702525e8cf52c59" FOREIGN KEY ("appointment_id") REFERENCES "appointments" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION
      )
    `);

    // 2. Copy data from the old table to the new table
    await queryRunner.query(`
      INSERT INTO "package_usage_history_new" 
      ("id", "customer_package_id", "appointment_id", "service_date", "status_at_usage", "notes", "event_type", "created_at")
      SELECT "id", "customer_package_id", "appointment_id", "service_date", "status_at_usage", "notes", "event_type", "created_at"
      FROM "package_usage_history"
    `);

    // 3. Drop the old table
    await queryRunner.query(`DROP TABLE "package_usage_history"`);

    // 4. Rename the new table
    await queryRunner.query(`ALTER TABLE "package_usage_history_new" RENAME TO "package_usage_history"`);

    // 5. Recreate indexes if any existed
    await queryRunner.query(`CREATE INDEX "IDX_10b53345d00197396e6db32d22e" ON "package_usage_history" ("customer_package_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_fa3bc8922073702525e8cf52c59" ON "package_usage_history" ("appointment_id")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Reverse the migration - make appointment_id NOT NULL again
    
    // 1. Create a new table with the original schema
    await queryRunner.query(`
      CREATE TABLE "package_usage_history_old" (
        "id" integer PRIMARY KEY AUTOINCREMENT NOT NULL,
        "customer_package_id" integer NOT NULL,
        "appointment_id" integer NOT NULL,
        "service_date" datetime NOT NULL,
        "status_at_usage" varchar NOT NULL,
        "notes" text,
        "event_type" varchar(50),
        "created_at" datetime NOT NULL DEFAULT (datetime('now')),
        CONSTRAINT "FK_10b53345d00197396e6db32d22e" FOREIGN KEY ("customer_package_id") REFERENCES "customer_packages" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION,
        CONSTRAINT "FK_fa3bc8922073702525e8cf52c59" FOREIGN KEY ("appointment_id") REFERENCES "appointments" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION
      )
    `);

    // 2. Copy data from the current table to the old table (excluding rows with NULL appointment_id)
    await queryRunner.query(`
      INSERT INTO "package_usage_history_old" 
      ("id", "customer_package_id", "appointment_id", "service_date", "status_at_usage", "notes", "event_type", "created_at")
      SELECT "id", "customer_package_id", "appointment_id", "service_date", "status_at_usage", "notes", "event_type", "created_at"
      FROM "package_usage_history"
      WHERE "appointment_id" IS NOT NULL
    `);

    // 3. Drop the current table
    await queryRunner.query(`DROP TABLE "package_usage_history"`);

    // 4. Rename the old table
    await queryRunner.query(`ALTER TABLE "package_usage_history_old" RENAME TO "package_usage_history"`);

    // 5. Recreate indexes
    await queryRunner.query(`CREATE INDEX "IDX_10b53345d00197396e6db32d22e" ON "package_usage_history" ("customer_package_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_fa3bc8922073702525e8cf52c59" ON "package_usage_history" ("appointment_id")`);
  }
}
