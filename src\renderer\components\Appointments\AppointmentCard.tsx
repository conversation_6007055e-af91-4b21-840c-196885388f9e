import React from 'react';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import IconButton from '@mui/material/IconButton';
import Avatar from '@mui/material/Avatar';
import Divider from '@mui/material/Divider';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import Grid from '@mui/material/Grid';
import Tooltip from '@mui/material/Tooltip';
import Zoom from '@mui/material/Zoom';
import {
  alpha,
  useTheme,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Pets as PetsIcon,
  Person as PersonIcon,
  Event as EventIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Notes as NotesIcon,
  Warning as WarningIcon,
  AccessTime as AccessTimeIcon,
  CheckCircle as CompletedIcon,
  HourglassEmpty as InProgressIcon,
  Cancel as CancelledIcon,
  PersonOff as NoShowIcon,
  CardGiftcard as CardGiftcardIcon
} from '@mui/icons-material';
import {
  Appointment,
  AppointmentStatusLabels,
  AppointmentStatusColors
} from '../../types/appointments';
import { formatBrazilianDate } from '../../utils/dateUtils';

interface AppointmentCardProps {
  appointment: Appointment;
  onEdit: (appointment: Appointment) => void;
  onDelete: (appointmentId: number) => void;
  onStatusChange: (appointmentId: number, newStatus: Appointment['status']) => void;
}

export const AppointmentCard: React.FC<AppointmentCardProps> = ({
  appointment,
  onEdit,
  onDelete,
  onStatusChange
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [statusMenuAnchorEl, setStatusMenuAnchorEl] = React.useState<null | HTMLElement>(null);
  const [hoveredStatus, setHoveredStatus] = React.useState(false);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleStatusMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setStatusMenuAnchorEl(event.currentTarget);
  };

  const handleStatusMenuClose = () => {
    setStatusMenuAnchorEl(null);
  };

  const handleStatusChange = (newStatus: Appointment['status']) => {
    onStatusChange(appointment.id, newStatus);
    handleStatusMenuClose();
    handleMenuClose();
  };

  const formatDate = (dateString: string) => {
    return formatBrazilianDate(dateString);
  };

  const getAppointmentTime = (dateString: string) => {
    return formatBrazilianDate(dateString, false);
  };

  // Check if appointment is about to expire
  const checkExpirationStatus = () => {
    const now = new Date();
    const appointmentDate = new Date(appointment.appointment_date);
    // For "no_show" appointments, check if they're fully expired (past noon the day after)
    if (appointment.status === 'no_show') {
      const expirationDate = new Date(appointmentDate);
      expirationDate.setDate(expirationDate.getDate() + 1);
      expirationDate.setHours(12, 0, 0, 0);
      if (now >= expirationDate) {
        return 'expired';
      } else {
        return 'pending-expiration';
      }
    }
    // If appointment has other final status, return null
    if (appointment.status === 'completed' ||
        appointment.status === 'cancelled' ||
        appointment.status === 'in_progress') {
      return null;
    }
    // Check if appointment is within next 30 minutes - UPCOMING CHECK
    if (appointment.status === 'scheduled') {
      const thirtyMinutesFromNow = new Date(now);
      thirtyMinutesFromNow.setMinutes(thirtyMinutesFromNow.getMinutes() + 30);
      const isUpcomingSoon = appointmentDate <= thirtyMinutesFromNow && appointmentDate > now;
      if (isUpcomingSoon) {
        return 'upcoming';
      }
    }
    // Create a DateTime 30 minutes after the appointment start time (grace period)
    const graceEndTime = new Date(appointmentDate);
    graceEndTime.setMinutes(graceEndTime.getMinutes() + 30);
    // Check if appointment date has passed AND grace period has passed
    if (appointmentDate < now && now > graceEndTime) {
      // Get next noon for expiration
      const expirationDate = new Date(appointmentDate);
      expirationDate.setDate(expirationDate.getDate() + 1);
      expirationDate.setHours(12, 0, 0, 0);
      // If the next day noon has passed, this is fully expired
      if (now >= expirationDate) {
        return 'expired';
      }
      // Appointment has passed but not fully expired yet
      return 'pending-expiration';
    }
    return null;
  };

  const expirationStatus = checkExpirationStatus();

  // Get border styling based on expiration status
  const getCardBorderStyle = () => {
    switch (expirationStatus) {
      case 'expired':
        return { border: '2px solid #f44336' }; // Red for fully expired
      case 'pending-expiration':
        return { border: '2px solid #ff9800' }; // Orange for pending expiration
      case 'upcoming':
        return { border: '2px solid #2196f3' }; // Blue for upcoming appointments
      default:
        return {};
    }
  };

  // Get expiration message
  const getExpirationMessage = () => {
    switch (expirationStatus) {
      case 'expired':
        return 'Este agendamento expirou completamente';
      case 'pending-expiration':
        return 'Este agendamento está marcado como faltou e expirará amanhã ao meio-dia';
      case 'upcoming':
        return 'Este agendamento está prestes a começar';
      default:
        return '';
    }
  };

  // Get status icon based on status
  const getStatusIcon = (status: Appointment['status']) => {
    switch (status) {
      case 'completed': return <CompletedIcon fontSize="small" />;
      case 'in_progress': return <InProgressIcon fontSize="small" />;
      case 'scheduled': return <ScheduleIcon fontSize="small" />;
      case 'cancelled': return <CancelledIcon fontSize="small" />;
      case 'no_show': return <NoShowIcon fontSize="small" />;
      default: return <ScheduleIcon fontSize="small" />;
    }
  };

  return (
    <Card sx={{
      height: '100%',
      width: '100%',
      maxWidth: 420,
      minWidth: 320,
      display: 'flex',
      flexDirection: 'column',
      position: 'relative',
      boxSizing: 'border-box',
      overflow: 'visible',
      ...getCardBorderStyle()
    }}>
      {expirationStatus && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            bgcolor: expirationStatus === 'expired' ? '#f44336' :
                     expirationStatus === 'pending-expiration' ? '#ff9800' : '#2196f3',
            color: 'white',
            px: 1,
            py: 0.5,
            borderBottomLeftRadius: 4,
            zIndex: 1,
            display: 'flex',
            alignItems: 'center',
            maxWidth: '60%',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          <Tooltip title={getExpirationMessage()}>
            <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 0 }}>
              {expirationStatus === 'expired' || expirationStatus === 'pending-expiration' ? (
                <WarningIcon fontSize="small" sx={{ mr: 0.5 }} />
              ) : (
                <AccessTimeIcon fontSize="small" sx={{ mr: 0.5 }} />
              )}
              <Typography variant="caption" fontWeight="bold" sx={{ minWidth: 0 }}>
                {expirationStatus === 'expired' ? 'EXPIRADO' :
                 expirationStatus === 'pending-expiration' ? 'FALTOU' : 'EM BREVE'}
              </Typography>
            </Box>
          </Tooltip>
        </Box>
      )}
      <Box sx={{ display: 'flex', p: 2, alignItems: 'center', bgcolor: '#f5f5f5' }}>
        <Avatar
          sx={{
            bgcolor: appointment.pet?.type ? getPetAvatarColor(appointment.pet.type) : '#9c27b0',
            width: 48,
            height: 48,
            mr: 2
          }}
        >
          <PetsIcon />
        </Avatar>
        <Box sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
            {appointment.is_package_appointment && (
              <Tooltip title="Este agendamento faz parte de um pacote">
                <CardGiftcardIcon color="secondary" sx={{ mr: 1 }} />
              </Tooltip>
            )}
            <Typography variant="h6" component="div">
              {appointment.pet?.name || 'Unknown Pet'}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
            <EventIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                textDecoration: expirationStatus === 'expired' || expirationStatus === 'pending-expiration' ? 'line-through' : 'none'
              }}
            >
              {formatDate(appointment.appointment_date)}
            </Typography>
          </Box>
        </Box>
        <Box>
          <Chip
            size="small"
            label={AppointmentStatusLabels[appointment.status]}
            icon={getStatusIcon(appointment.status)}
            onClick={handleStatusMenuOpen}
            onMouseEnter={() => setHoveredStatus(true)}
            onMouseLeave={() => setHoveredStatus(false)}
            sx={{
              bgcolor: alpha(AppointmentStatusColors[appointment.status], 0.1),
              color: AppointmentStatusColors[appointment.status],
              border: `1px solid ${alpha(AppointmentStatusColors[appointment.status], 0.2)}`,
              fontSize: '0.75rem',
              height: 28,
              fontWeight: 'medium',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              '&:hover': {
                bgcolor: alpha(AppointmentStatusColors[appointment.status], 0.2),
                boxShadow: `0 2px 4px ${alpha(AppointmentStatusColors[appointment.status], 0.2)}`,
                transform: 'translateY(-1px)'
              },
              '& .MuiChip-icon': {
                color: AppointmentStatusColors[appointment.status],
                marginLeft: '4px'
              },
              '& .MuiChip-label': {
                paddingLeft: '6px'
              }
            }}
          />
          <Menu
            anchorEl={statusMenuAnchorEl}
            open={Boolean(statusMenuAnchorEl)}
            onClose={handleStatusMenuClose}
            TransitionComponent={Zoom}
            PaperProps={{
              elevation: 3,
              sx: {
                minWidth: 180,
                borderRadius: 2,
                overflow: 'visible',
                mt: 1.5,
                '&:before': {
                  content: '""',
                  display: 'block',
                  position: 'absolute',
                  top: 0,
                  right: 14,
                  width: 10,
                  height: 10,
                  bgcolor: 'background.paper',
                  transform: 'translateY(-50%) rotate(45deg)',
                  zIndex: 0,
                },
              },
            }}
          >
            <MenuItem
              onClick={() => handleStatusChange('scheduled')}
              sx={{ py: 1.5 }}
            >
              <ListItemIcon>
                <ScheduleIcon fontSize="small" sx={{ color: AppointmentStatusColors.scheduled }} />
              </ListItemIcon>
              {AppointmentStatusLabels.scheduled}
            </MenuItem>

            <MenuItem
              onClick={() => handleStatusChange('in_progress')}
              sx={{ py: 1.5 }}
            >
              <ListItemIcon>
                <InProgressIcon fontSize="small" sx={{ color: AppointmentStatusColors.in_progress }} />
              </ListItemIcon>
              {AppointmentStatusLabels.in_progress}
            </MenuItem>

            <MenuItem
              onClick={() => handleStatusChange('completed')}
              sx={{ py: 1.5 }}
            >
              <ListItemIcon>
                <CompletedIcon fontSize="small" sx={{ color: AppointmentStatusColors.completed }} />
              </ListItemIcon>
              {AppointmentStatusLabels.completed}
            </MenuItem>

            <MenuItem
              onClick={() => handleStatusChange('cancelled')}
              sx={{ py: 1.5 }}
            >
              <ListItemIcon>
                <CancelledIcon fontSize="small" sx={{ color: AppointmentStatusColors.cancelled }} />
              </ListItemIcon>
              {AppointmentStatusLabels.cancelled}
            </MenuItem>

            <MenuItem
              onClick={() => handleStatusChange('no_show')}
              sx={{ py: 1.5 }}
            >
              <ListItemIcon>
                <NoShowIcon fontSize="small" sx={{ color: AppointmentStatusColors.no_show }} />
              </ListItemIcon>
              {AppointmentStatusLabels.no_show}
            </MenuItem>
          </Menu>
        </Box>
        <IconButton onClick={handleMenuOpen}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          TransitionComponent={Zoom}
          PaperProps={{
            elevation: 3,
            sx: {
              minWidth: 180,
              borderRadius: 2,
              overflow: 'visible',
              mt: 1.5,
              '&:before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0,
              },
            },
          }}
        >
          <MenuItem onClick={() => { handleMenuClose(); onEdit(appointment); }} sx={{ py: 1.5 }}>
            <ListItemIcon>
              <EditIcon fontSize="small" color="primary" />
            </ListItemIcon>
            Editar
          </MenuItem>
          <MenuItem
            onClick={() => { handleMenuClose(); onDelete(appointment.id); }}
            sx={{
              py: 1.5,
              color: 'error.main',
              '& .MuiListItemIcon-root': {
                color: 'error.main',
              }
            }}
          >
            <ListItemIcon>
              <DeleteIcon fontSize="small" />
            </ListItemIcon>
            Excluir
          </MenuItem>
        </Menu>
      </Box>
      <Divider />
      <CardContent sx={{ pt: 2, pb: 2, flexGrow: 1 }}>
        <Grid container spacing={1}>
          <Grid item xs={12} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <PersonIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography variant="body2">
              {appointment.customer?.name || 'Cliente Desconhecido'}
            </Typography>
          </Grid>

          <Grid item xs={12} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <ScheduleIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography variant="body2">
              {(() => {
                const serviceName = appointment.service?.name || 'Serviço Desconhecido';
                const bonusServiceName = appointment.bonusService?.name;
                const combinedName = bonusServiceName ? `${serviceName} + ${bonusServiceName}` : serviceName;
                const duration = appointment.service?.duration_minutes;
                const bonusDuration = appointment.bonusService?.duration_minutes;
                const totalDuration = duration && bonusDuration ? duration + bonusDuration : duration;
                return `${combinedName}${totalDuration ? ` (${totalDuration} min)` : ''}`;
              })()}
            </Typography>
          </Grid>

          {appointment.service?.price && (
            <Grid item xs={12} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <MoneyIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2">
                {(() => {
                  const servicePrice = appointment.service?.price || 0;
                  const bonusPrice = appointment.bonusService?.price || 0;
                  const totalPrice = servicePrice + bonusPrice;
                  return `R$ ${totalPrice.toFixed(2)}`;
                })()}
              </Typography>
            </Grid>
          )}
        </Grid>

        {appointment.notes && (
          <Box sx={{ mt: 2, p: 1.5, borderRadius: 1, bgcolor: '#f9f9f9', borderLeft: '3px solid #ccc' }}>
            <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
              {appointment.notes}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

const getPetAvatarColor = (type: string) => {
  switch (type.toLowerCase()) {
    case 'cachorro':
    case 'dog':
      return '#9c27b0';
    case 'gato':
    case 'cat':
      return '#2196f3';
    case 'ave':
    case 'bird':
      return '#ff9800';
    case 'peixe':
    case 'fish':
      return '#4caf50';
    case 'réptil':
    case 'reptile':
      return '#f44336';
    default:
      return '#9e9e9e';
  }
};