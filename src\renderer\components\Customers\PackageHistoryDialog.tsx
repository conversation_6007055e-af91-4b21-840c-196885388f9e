import React, { useState, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import IconButton from '@mui/material/IconButton';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import Paper from '@mui/material/Paper';
import Tooltip from '@mui/material/Tooltip';
import Chip from '@mui/material/Chip';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineContent,
  TimelineDot,
  TimelineConnector,
  TimelineOppositeContent
} from '@mui/lab';
import {
  History as HistoryIcon,
  CalendarToday as CalendarTodayIcon,
  Close as CloseIcon,
  Pets as PetsIcon,
  CommentOutlined as CommentIcon,
  CardGiftcard as CardGiftcardIcon,
  Spa as SpaIcon,
  ShoppingCart as ShoppingCartIcon,
  Check as CheckIcon,
  PlayArrow as PlayArrowIcon,
  Stop as StopIcon,
  DoneAll as DoneAllIcon,
  HourglassEmpty as HourglassEmptyIcon,
  EventAvailable as EventAvailableIcon,
  Payment as PaymentIcon
} from '@mui/icons-material';
import { PackageUsageHistory, CustomerPackage } from '../../types/packages';
import { formatDate } from '../../types/sales';
import { Appointment } from '../../types/appointments';

interface PackageHistoryDialogProps {
  open: boolean;
  onClose: () => void;
  packageName: string;
  customerName: string;
  getPackageUsageHistory: (customerPackageId: number) => Promise<PackageUsageHistory[]>;
  customerPackage: CustomerPackage | null;
}

export const PackageHistoryDialog: React.FC<PackageHistoryDialogProps> = ({
  open,
  onClose,
  packageName,
  customerName,
  getPackageUsageHistory,
  customerPackage,
}) => {
  const [usageHistory, setUsageHistory] = useState<PackageUsageHistory[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const customerPackageId = customerPackage?.id;
  const totalServices = customerPackage?.package?.total_occurrences ?? 0;
  const purchaseDateToFormat = customerPackage?.purchase_date ?? new Date();
  const expiryDateToFormat = customerPackage?.expiry_date ?? '';
  const nextScheduledAppointment = customerPackage?.nextScheduledAppointment;

  useEffect(() => {
    if (open && customerPackageId) {
      loadUsageHistory();
    } else if (!customerPackageId && open) {
      setError("Dados do pacote não encontrados.");
      setLoading(false);
    }
  }, [open, customerPackageId]);

  const loadUsageHistory = async () => {
    if (!customerPackageId) {
        setError("ID do Pacote do Cliente não fornecido.");
        setLoading(false);
        return;
    }
    setLoading(true);
    setError(null);
    try {
      const history = await getPackageUsageHistory(customerPackageId);
      setUsageHistory(history);
    } catch (err) {
      console.error('Error loading package usage history:', err);
      setError('Não foi possível carregar o histórico do pacote');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <HistoryIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6">
              Histórico do Pacote {packageName} - {customerName}
            </Typography>
          </Box>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <Divider />

      <DialogContent>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error">{error}</Alert>
        ) : (
          <Box>
            {/* Package summary */}
            <Paper
              elevation={0}
              sx={{
                p: 2,
                mb: 3,
                backgroundColor: 'background.paper',
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'divider'
              }}
            >
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Resumo do Pacote
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, alignItems: 'center' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CardGiftcardIcon sx={{ color: 'primary.main', mr: 1, fontSize: '1rem' }} />
                  <Typography variant="body2">
                    <strong>Total de Serviços:</strong> {totalServices}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CalendarTodayIcon sx={{ color: 'primary.main', mr: 1, fontSize: '1rem' }} />
                  <Typography variant="body2">
                    <strong>Data de Compra:</strong> {formatDate(purchaseDateToFormat.toString())}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CalendarTodayIcon sx={{ color: 'warning.main', mr: 1, fontSize: '1rem' }} />
                  <Typography variant="body2">
                    <strong>Data de Expiração:</strong>{' '}
                    {!expiryDateToFormat || expiryDateToFormat === '' ? 'Sem validade' : formatDate(expiryDateToFormat.toString())}
                  </Typography>
                </Box>
              </Box>
            </Paper>

            {/* Timeline */}
            {usageHistory.length === 0 && !nextScheduledAppointment ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <HistoryIcon sx={{ fontSize: 60, color: 'text.secondary', opacity: 0.5, mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  Nenhum uso registrado e nenhum próximo agendamento.
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Este pacote ainda não foi utilizado ou não tem próximos agendamentos.
                </Typography>
              </Box>
            ) : (
              <Timeline position="alternate">
                {/* Purchase event */}
                <TimelineItem sx={{ alignItems: 'center' }}>
                  <TimelineOppositeContent color="text.secondary">
                    {formatDate(purchaseDateToFormat.toString())}
                  </TimelineOppositeContent>
                  <TimelineSeparator sx={{ alignSelf: 'stretch' }}>
                    <TimelineDot color="primary">
                      <PlayArrowIcon />
                    </TimelineDot>
                    <TimelineConnector />
                  </TimelineSeparator>
                  <TimelineContent>
                    <Paper elevation={1} sx={{ p: 2, backgroundColor: 'primary.50' }}>
                      <Typography variant="subtitle2" component="span" fontWeight="bold">
                        Início do Pacote
                      </Typography>
                      <Typography variant="body2">
                        {packageName} adquirido por {customerName}
                      </Typography>
                    </Paper>
                  </TimelineContent>
                </TimelineItem>

                {/* Initial Next Scheduled Appointment if no usage yet */}
                {usageHistory.length === 0 && nextScheduledAppointment && customerPackage && customerPackage.status === 'active' && (
                  <TimelineItem sx={{ alignItems: 'center' }}>
                    <TimelineOppositeContent color="text.secondary">
                      {formatDate(nextScheduledAppointment.appointment_date)}
                    </TimelineOppositeContent>
                    <TimelineSeparator sx={{ alignSelf: 'stretch' }}>
                      <TimelineDot color="info">
                        <EventAvailableIcon />
                      </TimelineDot>
                      <TimelineConnector />
                    </TimelineSeparator>
                    <TimelineContent>
                      <Paper elevation={1} sx={{ p: 2, backgroundColor: 'info.50' }}>
                        <Typography variant="subtitle2" component="span" fontWeight="bold">
                          Próximo Agendamento
                        </Typography>
                        <Typography variant="body2">
                          {nextScheduledAppointment.service?.name || 'Serviço do pacote'}
                          {nextScheduledAppointment.pet ? ` para ${nextScheduledAppointment.pet.name}` : ''}
                        </Typography>
                      </Paper>
                    </TimelineContent>
                  </TimelineItem>
                )}

                {/* Usage events and subsequent Next Scheduled Appointments */}
                {usageHistory.map((usage, index) => (
                  <React.Fragment key={`usage-fragment-${usage.id}`}>
                    {/* Current Usage Event */}
                    <TimelineItem key={usage.id} sx={{ alignItems: 'center' }}>
                      <TimelineOppositeContent color="text.secondary">
                        {usage.service_date ? formatDate(usage.service_date.toString()) : 'Data desconhecida'}
                      </TimelineOppositeContent>
                      <TimelineSeparator sx={{ alignSelf: 'stretch' }}>
                        <TimelineDot
                          color={
                            usage.event_type === 'payment_completed' ? 'success' :
                            usage.event_type === 'no_show_resolved_rescheduled' || usage.event_type === 'no_show_resolved_cancelled' ? 'info' :
                            usage.status_at_usage === 'no_show' ? 'warning' :
                            usage.status_at_usage === 'cancelled' ? 'grey' :
                            usage.status_at_usage === 'completed' ? 'success' :
                            'success' // Default color for completed/other valid statuses
                          }
                        >
                          {
                            usage.event_type === 'payment_completed' ? <PaymentIcon /> :
                            usage.event_type === 'no_show_resolved_rescheduled' || usage.event_type === 'no_show_resolved_cancelled' ? <EventAvailableIcon /> :
                            usage.status_at_usage === 'no_show' ? <HourglassEmptyIcon /> :
                            usage.status_at_usage === 'cancelled' ? <StopIcon /> :
                            usage.status_at_usage === 'completed' ? <DoneAllIcon /> : // Icon for Agendamento Concluído
                            <SpaIcon /> // Fallback icon
                          }
                        </TimelineDot>
                        {/* Conditionally render connector unless it's the last item OR the next item is a 'nextScheduledAppointment' that shouldn't be connected to this usage */}
                        {(index < usageHistory.length - 1 || (index === usageHistory.length - 1 && nextScheduledAppointment && customerPackage && customerPackage.status === 'active')) &&
                          <TimelineConnector />
                        }
                      </TimelineSeparator>
                      <TimelineContent>
                        <Paper elevation={1} sx={{ p: 2, backgroundColor:
                          usage.event_type === 'payment_completed' ? 'success.50' :
                          usage.event_type === 'no_show_resolved_rescheduled' || usage.event_type === 'no_show_resolved_cancelled' ? 'info.50' :
                          usage.status_at_usage === 'no_show' ? 'warning.50' :
                          usage.status_at_usage === 'cancelled' ? 'grey.200' :
                          usage.status_at_usage === 'completed' ? 'success.50' :
                          'success.50'
                        }}>
                          <Typography variant="subtitle2" component="span" fontWeight="bold">
                            {
                              usage.event_type === 'payment_completed' ? 'Pagamento Realizado' :
                              usage.event_type === 'no_show_resolved_rescheduled' ? 'Resolução de Falta (Reagendado)' :
                              usage.event_type === 'no_show_resolved_cancelled' ? 'Resolução de Falta (Cancelado)' :
                              usage.status_at_usage === 'no_show' ? 'Faltou' :
                              usage.status_at_usage === 'cancelled' ? 'Pacote Cancelado' :
                              usage.status_at_usage === 'completed' ? 'Agendamento Concluído' :
                              'Evento do Pacote' // Default title
                            }
                          </Typography>
                          <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                            {(() => {
                              if (usage.event_type === 'payment_completed') {
                                return usage.notes || 'Pagamento do pacote foi completado';
                              } else if (usage.event_type === 'no_show_resolved_rescheduled' || usage.event_type === 'no_show_resolved_cancelled') {
                                return usage.notes || 'Detalhes não disponíveis';
                              } else if (usage.status_at_usage === 'no_show') {
                                return 'Cliente faltou a data marcada';
                              } else if (usage.status_at_usage === 'completed') {
                                // Show service information for completed appointments
                                const serviceName = usage.appointment?.service?.name || 'Serviço do pacote';
                                const bonusServiceName = usage.appointment?.bonusService?.name;
                                const combinedServiceName = bonusServiceName
                                  ? `${serviceName} + ${bonusServiceName}`
                                  : serviceName;
                                const baseText = `Serviço realizado: ${combinedServiceName}`;

                                // Add appointment notes if they exist
                                const appointmentNotes = usage.appointment?.notes;
                                let fullText = baseText;

                                if (appointmentNotes && appointmentNotes.trim()) {
                                  fullText += `\nObs: ${appointmentNotes.trim()}`;
                                }

                                // Add usage history notes if they exist (for backward compatibility)
                                if (usage.notes && usage.notes.trim()) {
                                  fullText += `\n${usage.notes}`;
                                }

                                return fullText;
                              } else if (usage.notes) {
                                return usage.notes;
                              }
                              return 'Detalhes não disponíveis';
                            })()}
                          </Typography>
                        </Paper>
                      </TimelineContent>
                    </TimelineItem>

                    {/* Display Next Scheduled Appointment after this usage, if applicable */}
                    {index === usageHistory.length - 1 && usage.event_type !== 'no_show_resolved_rescheduled' && nextScheduledAppointment && customerPackage && customerPackage.status === 'active' && customerPackage.next_scheduled_appointment_id === nextScheduledAppointment.id && (
                      <TimelineItem key={`next-appt-${usage.id}`} sx={{ alignItems: 'center' }}>
                        <TimelineOppositeContent color="text.secondary">
                          {formatDate(nextScheduledAppointment.appointment_date)}
                        </TimelineOppositeContent>
                        <TimelineSeparator sx={{ alignSelf: 'stretch' }}>
                          <TimelineDot color="info">
                            <EventAvailableIcon />
                          </TimelineDot>
                          {/* Connector logic: Show if it's not the absolute final event visualized.
                              If package will be completed/expired next, no connector from here. */}
                          {!(customerPackage.remaining_occurrences <= 1 && customerPackage.status === 'active') && <TimelineConnector />}
                        </TimelineSeparator>
                        <TimelineContent>
                          <Paper elevation={1} sx={{ p: 2, backgroundColor: 'info.50' }}>
                            <Typography variant="subtitle2" component="span" fontWeight="bold">
                              Próximo Agendamento
                            </Typography>
                            <Typography variant="body2">
                              {nextScheduledAppointment.service?.name || 'Serviço do pacote'}
                              {nextScheduledAppointment.pet ? ` para ${nextScheduledAppointment.pet.name}` : ''}
                            </Typography>
                          </Paper>
                        </TimelineContent>
                      </TimelineItem>
                    )}
                  </React.Fragment>
                ))}

                {/* Last event if all services used or expired */}
                {customerPackage && (customerPackage.status === 'completed' || customerPackage.status === 'expired') && usageHistory.length > 0 && (
                  <TimelineItem sx={{ alignItems: 'center' }}>
                    <TimelineOppositeContent color="text.secondary">
                      {/* Use the date of the last actual usage event for the "Pacote Concluído" timestamp */}
                      {usageHistory.length > 0 && usageHistory[usageHistory.length - 1].service_date ?
                        formatDate(usageHistory[usageHistory.length - 1].service_date.toString()) :
                        (customerPackage.updated_at ? formatDate(customerPackage.updated_at.toString()) : 'Data desconhecida')}
                    </TimelineOppositeContent>
                    <TimelineSeparator sx={{ alignSelf: 'stretch' }}>
                      <TimelineDot color={customerPackage.status === 'expired' ? "warning" : "error"}>
                        <StopIcon />
                      </TimelineDot>
                    </TimelineSeparator>
                    <TimelineContent>
                      <Paper elevation={1} sx={{ p: 2, backgroundColor: customerPackage.status === 'expired' ? 'warning.50' : 'error.50' }}>
                        <Typography variant="subtitle2" component="span" fontWeight="bold">
                          {customerPackage.status === 'expired' ? 'Pacote Expirado' : 'Pacote Concluído'}
                        </Typography>
                        <Typography variant="body2">
                          {customerPackage.status === 'expired' ?
                            `O pacote expirou em ${expiryDateToFormat ? formatDate(expiryDateToFormat.toString()) : 'data desconhecida'}.` :
                            'Todos os serviços deste pacote foram utilizados ou o pacote foi finalizado.'
                          }
                        </Typography>
                      </Paper>
                    </TimelineContent>
                  </TimelineItem>
                )}
              </Timeline>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="primary">
          Fechar
        </Button>
      </DialogActions>
    </Dialog>
  );
};