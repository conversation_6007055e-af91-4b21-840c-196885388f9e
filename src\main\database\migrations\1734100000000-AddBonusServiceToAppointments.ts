import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBonusServiceToAppointments1734100000000 implements MigrationInterface {
  name = 'AddBonusServiceToAppointments1734100000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add bonus_service_id column to appointments table
    await queryRunner.query(`
      ALTER TABLE "appointments" 
      ADD COLUMN "bonus_service_id" integer
    `);

    // Add foreign key constraint for bonus_service_id
    await queryRunner.query(`
      CREATE INDEX "IDX_bonus_service_id" ON "appointments" ("bonus_service_id")
    `);

    // Note: SQLite doesn't support adding foreign key constraints to existing tables
    // The foreign key relationship will be enforced by TypeORM at the application level
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the index
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_bonus_service_id"`);
    
    // Remove the bonus_service_id column
    // SQLite doesn't support DROP COLUMN directly, so we need to recreate the table
    
    // 1. Create a new table without the bonus_service_id column
    await queryRunner.query(`
      CREATE TABLE "appointments_backup" (
        "id" integer PRIMARY KEY AUTOINCREMENT NOT NULL,
        "appointment_date" datetime NOT NULL,
        "status" varchar NOT NULL DEFAULT ('scheduled'),
        "notes" varchar,
        "is_package_appointment" boolean NOT NULL DEFAULT (0),
        "source_customer_package_id" integer,
        "created_at" datetime NOT NULL DEFAULT (datetime('now')),
        "updated_at" datetime NOT NULL DEFAULT (datetime('now')),
        "customerId" integer,
        "petId" integer,
        "serviceId" integer,
        CONSTRAINT "FK_60dbcf20669c096d319e20fca8a" FOREIGN KEY ("customerId") REFERENCES "customers" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION,
        CONSTRAINT "FK_96e11d40768b1eea9dddc38a124" FOREIGN KEY ("petId") REFERENCES "pets" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION,
        CONSTRAINT "FK_f77953c373efb8ab146d98e90c3" FOREIGN KEY ("serviceId") REFERENCES "services" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION,
        CONSTRAINT "FK_b8549cb441e8f439af8aaa50cd2" FOREIGN KEY ("source_customer_package_id") REFERENCES "customer_packages" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION
      )
    `);

    // 2. Copy data from the current table to the backup table (excluding bonus_service_id)
    await queryRunner.query(`
      INSERT INTO "appointments_backup" 
      ("id", "appointment_date", "status", "notes", "is_package_appointment", "source_customer_package_id", "created_at", "updated_at", "customerId", "petId", "serviceId")
      SELECT "id", "appointment_date", "status", "notes", "is_package_appointment", "source_customer_package_id", "created_at", "updated_at", "customerId", "petId", "serviceId"
      FROM "appointments"
    `);

    // 3. Drop the current table
    await queryRunner.query(`DROP TABLE "appointments"`);

    // 4. Rename the backup table
    await queryRunner.query(`ALTER TABLE "appointments_backup" RENAME TO "appointments"`);
  }
}
