import { AppDataSource } from '../connection';
import { Appointment } from '../models/Appointment';
import { Between, MoreThanOrEqual } from 'typeorm';
import { CustomerPackageService } from './CustomerPackageService';

export class AppointmentService {
  private repository = AppDataSource.getRepository(Appointment);
  private customerPackageService = new CustomerPackageService();

  async findAll(): Promise<Appointment[]> {
    return this.repository.find({
      relations: ['customer', 'pet', 'service', 'bonusService'],
      order: { appointment_date: 'ASC' }
    });
  }

  async findById(id: number): Promise<Appointment | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['customer', 'pet', 'service', 'bonusService']
    });
  }

  async findByCustomerId(customerId: number): Promise<Appointment[]> {
    return this.repository.find({
      where: { customer: { id: customerId } },
      relations: ['customer', 'pet', 'service', 'bonusService'],
      order: { appointment_date: 'ASC' }
    });
  }

  async findByPetId(petId: number): Promise<Appointment[]> {
    return this.repository.find({
      where: { pet: { id: petId } },
      relations: ['customer', 'pet', 'service', 'bonusService'],
      order: { appointment_date: 'ASC' }
    });
  }

  async findUpcoming(): Promise<Appointment[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return this.repository.find({
      where: {
        appointment_date: MoreThanOrEqual(today),
        status: 'scheduled'
      },
      relations: ['customer', 'pet', 'service', 'bonusService'],
      order: { appointment_date: 'ASC' }
    });
  }

  async findByDateRange(startDate: Date, endDate: Date): Promise<Appointment[]> {
    return this.repository.find({
      where: {
        appointment_date: Between(startDate, endDate)
      },
      relations: ['customer', 'pet', 'service', 'bonusService'],
      order: { appointment_date: 'ASC' }
    });
  }

  async create(appointmentData: Partial<Appointment>): Promise<Appointment> {
    const appointment = this.repository.create(appointmentData);
    return this.repository.save(appointment);
  }

  async update(id: number, appointmentData: Partial<Appointment>): Promise<Appointment | null> {
    await this.repository.update(id, appointmentData);
    return this.findById(id);
  }

  async updateStatus(id: number, status: string): Promise<Appointment | null> {
    await this.repository.update(id, { status });
    const updated = await this.findById(id);
    if (updated && updated.is_package_appointment && status === 'completed') {
      await this.customerPackageService.handlePackageAppointmentCompletion(id);
    }
    return updated;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== undefined && result.affected !== null && result.affected > 0;
  }

  /**
   * Verifica todos os agendamentos com status 'scheduled' cujo horário + 30min já passou,
   * marca como 'no_show' e, se for de pacote, chama o handler de pacote.
   * Pode ser chamada no startup e no foco da janela principal.
   */
  static async catchUpNoShows() {
    const repository = AppDataSource.getRepository(Appointment);
    const customerPackageService = new CustomerPackageService();

    // Get current time as a UTC string: YYYY-MM-DD HH:MM:SS
    const nowUTC = new Date();
    const yearUTC = nowUTC.getUTCFullYear();
    const monthUTC = (nowUTC.getUTCMonth() + 1).toString().padStart(2, '0');
    const dayUTC = nowUTC.getUTCDate().toString().padStart(2, '0');
    const hourUTC = nowUTC.getUTCHours().toString().padStart(2, '0');
    const minuteUTC = nowUTC.getUTCMinutes().toString().padStart(2, '0');
    const secondUTC = nowUTC.getUTCSeconds().toString().padStart(2, '0');
    const nowUTCStr = `${yearUTC}-${monthUTC}-${dayUTC} ${hourUTC}:${minuteUTC}:${secondUTC}`;

    const overdue = await repository
      .createQueryBuilder('appointment')
      .where('appointment.status = :status', { status: 'scheduled' })
      // Compare appointment_date (which is UTC) with nowUTCStr (also UTC)
      .andWhere('datetime(appointment.appointment_date, "+30 minutes") < :now', { now: nowUTCStr })
      .getMany();

    for (const appt of overdue) {
      appt.status = 'no_show';
      await repository.save(appt);

      if (appt.is_package_appointment && appt.source_customer_package_id) {
        try {
          await customerPackageService.handlePackageAppointmentNoShow(appt.id);
        } catch (error) {
          // Keep error logs for actual issues
        }
      }
    }
  }
}