import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Customer } from './Customer';
import { Pet } from './Pet';
import { Service } from './Service';
import { CustomerPackage } from './CustomerPackage';

@Entity('appointments')
export class Appointment {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => Customer)
  customer!: Customer;

  @ManyToOne(() => Pet)
  pet!: Pet;

  @ManyToOne(() => Service)
  service!: Service;

  @Column({ nullable: true })
  bonus_service_id!: number | null;

  @ManyToOne(() => Service, { nullable: true })
  @JoinColumn({ name: 'bonus_service_id' })
  bonusService?: Service | null;

  @Column('datetime')
  appointment_date!: Date;

  @Column({ default: 'scheduled' })
  status!: string;

  @Column({ nullable: true })
  notes!: string;

  @Column({ default: false })
  is_package_appointment!: boolean;

  @Column({ nullable: true })
  source_customer_package_id!: number | null;

  @ManyToOne(() => CustomerPackage, { nullable: true })
  @JoinColumn({ name: 'source_customer_package_id' })
  sourceCustomerPackage?: CustomerPackage | null;

  @CreateDateColumn()
  created_at!: Date;

  @UpdateDateColumn()
  updated_at!: Date;
}