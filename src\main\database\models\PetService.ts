import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON>n, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Pet } from './Pet';

@Entity('pet_services')
export class PetService {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  pet_id!: number;

  @ManyToOne(() => Pet, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'pet_id' })
  pet!: Pet;

  @Column()
  service_name!: string;

  @Column()
  service_date!: Date;

  @Column({ nullable: true })
  notes!: string;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  price_paid!: number | null;

  @Column({ type: 'varchar', nullable: true })
  source_type!: 'sale' | 'package' | null;

  @Column({ type: 'integer', nullable: true })
  source_id!: number | null;

  @CreateDateColumn()
  created_at!: Date;

  @UpdateDateColumn()
  updated_at!: Date;
}