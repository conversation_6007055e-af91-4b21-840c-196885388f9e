import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, CreateDateColumn, Join<PERSON><PERSON>umn } from 'typeorm';
import { CustomerPackage } from './CustomerPackage';
import { Appointment } from './Appointment';

export type PackageUsageStatus = 'completed' | 'no_show' | 'system_cancelled' | 'active' | 'cancelled';

@Entity('package_usage_history')
export class PackageUsageHistory {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  customer_package_id!: number;

  @ManyToOne(() => CustomerPackage)
  @JoinColumn({ name: 'customer_package_id' })
  customerPackage!: CustomerPackage;

  @Column({ nullable: true })
  appointment_id!: number | null;

  @ManyToOne(() => Appointment, { nullable: true })
  @JoinColumn({ name: 'appointment_id' })
  appointment?: Appointment | null;

  @Column('datetime')
  service_date!: Date;

  @Column({ type: 'varchar' })
  status_at_usage!: PackageUsageStatus;

  @Column({ nullable: true, type: 'text' })
  notes!: string | null;

  @Column({ nullable: true, type: 'varchar', length: 50 })
  event_type?: string;

  @CreateDateColumn()
  created_at!: Date;
}